# LexiGen Critical Issues - Reproduction Scenarios

## Issue 1: Document Activity Log Display Failure

### **Before Fix - Reproduction Steps**:
1. Log into LexiGen application
2. Create a new document
3. Edit the document content
4. Open the document detail page
5. Click on the utility panel and select "Activity" tab
6. **Expected**: See activity logs for document creation and editing
7. **Actual**: Empty activity panel with "No activity recorded yet."

### **Root Cause**: 
- No API calls to create activity logs when actions are performed
- Activity log endpoints exist but are never called

### **After Fix - Verification Steps**:
1. Apply the migration: `supabase db push`
2. Deploy updated server code with activity logging
3. Create a new document
4. Check activity panel - should show "Created document [name]"
5. Edit document - should show "Edited document [name]"

---

## Issue 2: Comment Integration Failure in Utility Panel

### **Before Fix - Reproduction Steps**:
1. Open a document in LexiGen
2. Highlight some text in the document
3. Click the "Add Comment" button that appears
4. **Expected**: Comment thread created and visible in Comments panel
5. **Actual**: Nothing happens, no comment thread created

### **Root Cause**:
- `onAddComment` function only creates temporary ID
- No API call to `POST /api/comments/threads`
- No state update to add thread to document

### **After Fix - Verification Steps**:
1. Deploy updated frontend code
2. Highlight text in document
3. Click "Add Comment" button
4. Should see comment thread created in Comments panel
5. Should be able to add replies to the thread

---

## Issue 3: AI Document Generation from Templates

### **Before Fix - Investigation Result**:
**This issue is actually WORKING correctly**

### **Verification Steps**:
1. Go to Templates page
2. Select any template (e.g., "Draft a simple NDA")
3. Click "Use Template"
4. **Expected**: Redirected to generate page with template prompt
5. **Actual**: Should work correctly (this was a false positive)

### **Workflow**:
- Template selection → `onSelectTemplate(prompt)` → `handleSelectTemplate` → Generate view

---

## Issue 4: Chat Interface Document Persistence

### **Before Fix - Investigation Result**:
**This issue is actually WORKING correctly**

### **Verification Steps**:
1. Go to chat interface
2. Generate a document (e.g., "Draft a simple contract")
3. Click "Save Document" when prompted
4. Fill in document name and save
5. **Expected**: Document saved to database and visible in document list
6. **Actual**: Should work correctly (this was a false positive)

### **Workflow**:
- Chat generation → Save modal → `handleSaveFromModal` → `onSaveDocument` → API call

---

## Database Schema Issues

### **Version Type Column Missing**:
1. Try to create a document version through API
2. **Before Fix**: May fail with database error about missing column
3. **After Fix**: Should work correctly with version_type defaulting to 'auto'

---

## Testing Commands

### **Run Critical Workflow Tests**:
```bash
npm test -- tests/critical-workflow-fixes.test.ts
```

### **Run Full Test Suite**:
```bash
npm test
```

### **Apply Database Migration**:
```bash
supabase db push
```

### **Verify Database Schema**:
```sql
-- Check if version_type column exists
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_name = 'document_versions' 
AND column_name = 'version_type';

-- Check activity logs structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'activity_logs';

-- Verify indexes exist
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename IN ('activity_logs', 'comment_threads', 'document_versions');
```

---

## Manual Testing Checklist

### **Activity Logs**:
- [ ] Create document → Check activity panel shows creation log
- [ ] Edit document → Check activity panel shows edit log
- [ ] Share document → Check activity panel shows share log
- [ ] Add comment → Check activity panel shows comment log

### **Comments**:
- [ ] Highlight text → Click add comment → Thread appears in panel
- [ ] Add reply to thread → Reply appears in thread
- [ ] Resolve thread → Thread marked as resolved
- [ ] Delete comment → Comment removed from thread

### **Templates**:
- [ ] Select template → Redirected to generate page
- [ ] Generate document from template → Document created
- [ ] Template prompt appears in chat interface

### **Chat Persistence**:
- [ ] Generate document in chat → Save document → Appears in document list
- [ ] Edit saved document → Changes persist
- [ ] Document has correct metadata and content

---

## Performance Verification

### **Database Query Performance**:
```sql
-- Test activity logs query performance
EXPLAIN ANALYZE 
SELECT * FROM activity_logs 
WHERE document_id = 'test-doc-id' 
ORDER BY timestamp DESC;

-- Test comment threads query performance
EXPLAIN ANALYZE 
SELECT ct.*, c.* 
FROM comment_threads ct 
LEFT JOIN comments c ON c.thread_id = ct.id 
WHERE ct.document_id = 'test-doc-id';
```

### **Expected Results**:
- Activity logs query should use index on (document_id, timestamp)
- Comment threads query should use index on document_id
- All queries should complete in < 10ms for typical document sizes
