import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { Document as DocType, User, DashboardView, Collaborator, Signature, Clause, Obligation, Team, WorkflowInstance, WorkflowTemplate } from '../types';
import { But<PERSON> } from './ui/Button';
import { ArrowLeftIcon, EditIcon, SaveIcon, CloseIcon, ShareIcon, SignatureIcon, MoreVerticalIcon, LibraryIcon, ClockIcon, CheckCircleIcon, MessageSquarePlusIcon, UsersIcon, PanelRightOpenIcon, ChevronDownIcon, PdfIcon, WordIcon } from './Icons';
import DocumentRenderer from './DocumentRenderer';
import EditorToolbar from './EditorToolbar';
import { exportAsPdf, exportAsDocx } from '../lib/file-export';
import DocumentUtilityPanel from './DocumentUtilityPanel';
import ShareModal from './ShareModal';
import SaveTemplateModal from './SaveTemplateModal';
import RequestSignatureModal from './RequestSignatureModal';
import { cn } from '../lib/utils';
import ClauseEditorModal from './ClauseEditorModal';
import InsertClauseModal from './InsertClauseModal';
import Dropdown from './ui/Dropdown';
import RequestApprovalModal from './RequestApprovalModal';
import WorkflowStatusBanner from './WorkflowStatusBanner';


type UtilityTab = 'suggestions' | 'history' | 'comments' | 'analysis' | 'signatures' | 'activity' | 'approvals' | 'obligations';

interface DocumentDetailPageProps {
  document: DocType;
  onBack: () => void;
  onSave: (documentId: string, newContent: string) => void;
  onRevert: (documentId: string, versionId: string) => void;
  user: User;
  allUsers: User[];
  allTeams: Team[];
  setView: (view: DashboardView) => void;
  onUpdateCollaborators: (documentId: string, collaborators: Collaborator[]) => void;
  onAddComment: (documentId: string, textSelection: string) => string | undefined;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  initialUtilityTab: 'suggestions' | 'history' | 'comments' | 'analysis' | null;
  onClearInitialTab: () => void;
  onCreateCustomTemplate: (docId: string, templateName: string) => void;
  onRequestSignatures: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
  onCreateClause: (clauseData: Omit<Clause, 'id' | 'createdAt'>) => void;
  onUpdateDocumentClient: (documentId: string, clientId: string | null) => void;
  onRequestApproval: (docId: string, approverEmails: string[]) => void;
  onRespondToApproval: (docId: string, decision: 'approved' | 'changes-requested', comments?: string) => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
  workflowInstances: WorkflowInstance[];
}

const TooltipButton: React.FC<{ title: string; children: React.ReactNode; onClick?: () => void; disabled?: boolean; variant?: 'default' | 'outline' | 'ghost' | 'destructive'; className?: string;}> = ({ title, children, onClick, disabled, variant = 'ghost', className }) => (
    <div className="relative group">
        <Button variant={variant} size="icon" onClick={onClick} disabled={disabled} aria-label={title} className={className}>
            {children}
        </Button>
        <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap" role="tooltip">
            {title}
        </div>
    </div>
);


const DocumentDetailPage: React.FC<DocumentDetailPageProps> = memo((props) => {
  const { document, onBack, onSave, onRevert, user, allUsers, allTeams, setView, onUpdateCollaborators, onAddComment, onAddReply, onResolveThread, onDeleteComment, initialUtilityTab, onClearInitialTab, onCreateCustomTemplate, onRequestSignatures, onCreateClause, onUpdateDocumentClient, onRequestApproval, onRespondToApproval: _onRespondToApproval, onUpdateObligationStatus, workflowInstances } = props;
  const [isEditing, setIsEditing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isUtilityPanelOpen, setIsUtilityPanelOpen] = useState(false);
  
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isSaveTemplateModalOpen, setIsSaveTemplateModalOpen] = useState(false);
  const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState<{ top: number, left: number } | null>(null);
  const [isClauseEditorOpen, setIsClauseEditorOpen] = useState(false);
  const [clauseContentToSave, setClauseContentToSave] = useState<string | null>(null);
  const [isInsertClauseModalOpen, setIsInsertClauseModalOpen] = useState(false);

  const editorRef = useRef<HTMLDivElement>(null);
  const pageContainerRef = useRef<HTMLDivElement>(null);

  const [committedContent, setCommittedContent] = useState(document.content);

  const runningWorkflowInstance = workflowInstances.find(inst => inst.documentId === document.id && inst.status === 'running');
  
  let runningWorkflowTemplate: WorkflowTemplate | undefined;
  if (runningWorkflowInstance) {
      for (const team of allTeams) {
          const foundTemplate = team.workflows?.find(t => t.id === runningWorkflowInstance.templateId);
          if (foundTemplate) {
              runningWorkflowTemplate = foundTemplate;
              break;
          }
      }
  }

  const isWorkflowActive = !!runningWorkflowInstance;

  const isLockedForSigning = document.status === 'out-for-signature' || document.status === 'completed' || document.status === 'archived';
  const isPremiumFeatureEnabled = user.planName === 'Premium' || user.planName === 'Enterprise';
  const client = user.clients?.find(c => c.id === document.clientId);

  const [activeTool, setActiveTool] = useState<UtilityTab | null>(isPremiumFeatureEnabled ? 'activity' : null);


  useEffect(() => {
      setCommittedContent(document.content);
      if (isLockedForSigning || isWorkflowActive) {
        setIsEditing(false); // Force exit edit mode if locked
      }
  }, [document, isLockedForSigning, isWorkflowActive]);

  useEffect(() => {
    if (initialUtilityTab) {
        setActiveTool(initialUtilityTab as UtilityTab);
        onClearInitialTab(); // Reset the one-time prop
    }
  }, [initialUtilityTab, onClearInitialTab]);
  
  const handleSelection = useCallback(() => {
    if (!isEditing || isLockedForSigning || isWorkflowActive) {
        setSelectionPosition(null);
        return;
    }
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (editorRef.current?.contains(range.commonAncestorContainer)) {
            const rect = range.getBoundingClientRect();
            const editorRect = editorRef.current.getBoundingClientRect();
            setSelectionPosition({
                top: rect.top - editorRect.top - 10,
                left: rect.left - editorRect.left + rect.width / 2,
            });
        } else {
            setSelectionPosition(null);
        }
    } else {
        setSelectionPosition(null);
    }
  }, [isEditing, isLockedForSigning, isWorkflowActive]);
  
  useEffect(() => {
      window.document.addEventListener('selectionchange', handleSelection);
      return () => window.document.removeEventListener('selectionchange', handleSelection);
  }, [handleSelection]);


  const handleSave = () => {
    if (editorRef.current) {
        const newContent = editorRef.current.innerHTML;
        onSave(document.id, newContent);
        setCommittedContent(newContent);
        setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };
  
  const handleInsertClause = async (clauseContent: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      const sel = window.getSelection();
      if (sel && sel.rangeCount > 0) {
        const range = sel.getRangeAt(0);
        range.deleteContents();
        const div = window.document.createElement('div');
        // Sanitize inserted clause HTML to prevent XSS
        try {
          const { sanitizeHtml } = await import('../lib/sanitize');
          div.innerHTML = `<br>${sanitizeHtml(clauseContent)}`;
        } catch {
          div.innerHTML = `<br>`;
        }
        const frag = window.document.createDocumentFragment();
        let node, lastNode;
        while ((node = div.firstChild)) {
            lastNode = frag.appendChild(node);
        }
        range.insertNode(frag);

        if (lastNode) {
            const newRange = range.cloneRange();
            newRange.setStartAfter(lastNode);
            newRange.collapse(true);
            sel.removeAllRanges();
            sel.addRange(newRange);
        }
      }
    }
  };

  const handleExport = async (format: 'pdf' | 'docx') => {
    setIsExporting(true);
    try {
        if (format === 'pdf' && editorRef.current) {
            await exportAsPdf(editorRef.current, document.name);
        } else if (format === 'docx') {
            await exportAsDocx(committedContent, document.name);
        }
    } catch {
        // Failed to export document
        alert(`An error occurred while exporting to ${format}. Please try again.`);
    } finally {
        setIsExporting(false);
    }
  };
  
  const handleCollaboratorsUpdate = (updatedCollaborators: Collaborator[]) => {
      onUpdateCollaborators(document.id, updatedCollaborators);
  };

  const handleAddCommentClick = () => {
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.rangeCount > 0) {
        const textSelection = selection.toString();
        const threadId = onAddComment(document.id, textSelection);

        if (threadId) {
            const range = selection.getRangeAt(0);
            const span = window.document.createElement('span');
            span.className = 'comment-highlight';
            span.dataset.commentThreadId = threadId;
            range.surroundContents(span);
            setSelectionPosition(null); // Hide button after creating comment
            setActiveTool('comments');
        }
    }
  };
  
  const handleEditorClick = (e: React.MouseEvent<HTMLDivElement>) => {
      const target = e.target as HTMLElement;
      const highlightSpan = target.closest('.comment-highlight');
      if (highlightSpan) {
          const threadId = highlightSpan.getAttribute('data-comment-thread-id');
          if (threadId) {
              setActiveTool('comments');
              setTimeout(() => {
                  const commentEl = window.document.getElementById(`comment-thread-${threadId}`);
                  commentEl?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  window.document.querySelectorAll('.comment-highlight').forEach(el => el.classList.remove('active'));
                  highlightSpan.classList.add('active');
              }, 100);
          }
      }
  };

  const handleSaveTemplate = (templateName: string) => {
    onCreateCustomTemplate(document.id, templateName);
    setIsSaveTemplateModalOpen(false);
  };

  const handleSaveClauseClick = () => {
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const content = range.cloneContents();
      const div = window.document.createElement('div');
      div.appendChild(content);
      setClauseContentToSave(div.innerHTML);
      setIsClauseEditorOpen(true);
    }
    setSelectionPosition(null);
  };
  
  const statusBanner = () => {
    const banners: Record<DocType['status'], { text: string; icon: React.FC<{ className?: string }>; classes: string } | null> = {
        'in-review': { text: 'This document is currently in review and cannot be edited.', icon: ClockIcon, classes: 'bg-amber-100 border-amber-200 text-amber-800 dark:bg-amber-900/30 dark:border-amber-800 dark:text-amber-200' },
        'approved': { text: 'This document has been approved and is ready for signature.', icon: CheckCircleIcon, classes: 'bg-sky-100 border-sky-200 text-sky-800 dark:bg-sky-900/30 dark:border-sky-800 dark:text-sky-200' },
        'out-for-signature': { text: 'This document is out for signature and cannot be edited.', icon: ClockIcon, classes: 'bg-blue-100 border-blue-200 text-blue-800 dark:bg-blue-900/30 dark:border-blue-800 dark:text-blue-200' },
        'completed': { text: 'This document has been completed and signed by all parties.', icon: CheckCircleIcon, classes: 'bg-green-100 border-green-200 text-green-800 dark:bg-green-900/30 dark:border-green-800 dark:text-green-200' },
        'archived': { text: 'This document is archived and cannot be edited.', icon: ClockIcon, classes: 'bg-zinc-100 border-zinc-200 text-zinc-800 dark:bg-zinc-800/30 dark:border-zinc-700 dark:text-zinc-200' },
        'draft': null
    }
    const banner = banners[document.status];
    if (!banner || isWorkflowActive) {return null;} // Don't show status banner if workflow banner is active
    const Icon = banner.icon;
    return (
        <div className={cn("p-3 border-b text-center text-sm flex items-center justify-center gap-2", banner.classes)}>
          <Icon className="w-5 h-5" />
          {banner.text}
        </div>
    );
  };


  return (
    <>
    <div className="flex h-full">
      <div className="flex-1 flex flex-col bg-white dark:bg-zinc-950">
        <div className="flex-shrink-0 p-4 sm:p-6 lg:p-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex items-center gap-4">
                    <TooltipButton title="Back to Documents" onClick={onBack}>
                        <ArrowLeftIcon className="h-5 w-5" />
                    </TooltipButton>
                    <div>
                        <h1 className="text-2xl font-bold text-zinc-900 dark:text-white">{document.name}</h1>
                        <div className="flex items-center flex-wrap gap-x-4 gap-y-1 text-sm text-zinc-500 dark:text-zinc-400">
                            <span>Last updated on {new Date(document.updatedAt).toLocaleString()}</span>
                             <div className="flex items-center gap-1.5">
                                <UsersIcon className="w-4 h-4" />
                                <span>Client:</span>
                                <Dropdown>
                                    <Dropdown.Trigger>
                                        <Button variant="ghost" className="h-auto p-0 font-medium text-brand-600 dark:text-brand-400">
                                            {client?.name || 'Unassigned'}
                                            <ChevronDownIcon className="w-4 h-4 ml-1" />
                                        </Button>
                                    </Dropdown.Trigger>
                                    <Dropdown.Content align="left">
                                        <Dropdown.Item onClick={() => onUpdateDocumentClient(document.id, null)} icon={<CloseIcon className="w-4 h-4"/>}>Unassigned</Dropdown.Item>
                                        <Dropdown.Separator />
                                        {(user.clients || []).map(c => (
                                            <Dropdown.Item key={c.id} onClick={() => onUpdateDocumentClient(document.id, c.id)} icon={<UsersIcon className="w-4 h-4"/>}>
                                                {c.name}
                                            </Dropdown.Item>
                                        ))}
                                    </Dropdown.Content>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                    <div className="flex -space-x-2">
                        {document.collaborators.map(c => (
                            <img key={c.email} src={c.avatarUrl} alt={c.email} title={c.email} className="w-8 h-8 rounded-full border-2 border-white dark:border-zinc-950"/>
                        ))}
                    </div>
                </div>
                <div className="flex items-center gap-1.5 flex-wrap">
                    {isEditing ? (
                        <>
                            {isPremiumFeatureEnabled && (
                                <TooltipButton title="Insert Clause" onClick={() => setIsInsertClauseModalOpen(true)}>
                                    <LibraryIcon className="w-5 h-5" />
                                </TooltipButton>
                            )}
                            <TooltipButton title="Cancel" onClick={handleCancel}>
                                <CloseIcon className="w-5 h-5" />
                            </TooltipButton>
                            <TooltipButton title="Save Changes" onClick={handleSave} variant="default">
                                <SaveIcon className="w-5 h-5 text-white" />
                            </TooltipButton>
                        </>
                    ) : (
                        <>
                            {isPremiumFeatureEnabled && document.status === 'draft' && (
                                <TooltipButton title="Request Signatures" onClick={() => setIsSignatureModalOpen(true)} disabled={isWorkflowActive}>
                                    <SignatureIcon className="w-5 h-5"/>
                                </TooltipButton>
                            )}
                             <TooltipButton title="Share" onClick={() => setIsShareModalOpen(true)}>
                                <ShareIcon className="w-5 h-5" />
                            </TooltipButton>
                            
                            <TooltipButton title="Edit Document" onClick={() => setIsEditing(true)} disabled={isLockedForSigning || isWorkflowActive}>
                                <EditIcon className="w-5 h-5"/>
                            </TooltipButton>
                            
                            <Dropdown>
                                <Dropdown.Trigger>
                                     <TooltipButton title="More Actions" disabled={isExporting}>
                                        <MoreVerticalIcon className="w-5 h-5" />
                                    </TooltipButton>
                                </Dropdown.Trigger>
                                <Dropdown.Content>
                                    {isPremiumFeatureEnabled && document.status === 'draft' && (
                                        <Dropdown.Item onClick={() => setIsApprovalModalOpen(true)} disabled={isWorkflowActive} icon={<UsersIcon className="w-4 h-4"/>}>
                                            Request Approval
                                        </Dropdown.Item>
                                    )}
                                    {isPremiumFeatureEnabled && !isLockedForSigning && (
                                        <Dropdown.Item onClick={() => setIsSaveTemplateModalOpen(true)} icon={<SaveIcon className="w-4 h-4"/>}>
                                            Save as Template
                                        </Dropdown.Item>
                                    )}
                                    <Dropdown.Item onClick={() => handleExport('pdf')} icon={<PdfIcon className="w-4 h-4 text-red-600"/>}>
                                        {isExporting ? 'Exporting...' : 'Export as PDF'}
                                    </Dropdown.Item>
                                    <Dropdown.Item onClick={() => handleExport('docx')} icon={<WordIcon className="w-4 h-4 text-blue-600"/>}>
                                        {isExporting ? 'Exporting...' : 'Export as Word'}
                                    </Dropdown.Item>
                                </Dropdown.Content>
                            </Dropdown>
                             {isPremiumFeatureEnabled && (
                                <TooltipButton title="Toggle Tools" onClick={() => setIsUtilityPanelOpen(p => !p)} variant="outline" className="md:hidden">
                                    <PanelRightOpenIcon className="w-5 h-5" />
                                </TooltipButton>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
        
        {isWorkflowActive && runningWorkflowTemplate && (
            <WorkflowStatusBanner
                instance={runningWorkflowInstance}
                template={runningWorkflowTemplate}
            />
        )}
        {statusBanner()}

        <div className="flex-1 overflow-y-auto page-container" ref={pageContainerRef}>
            <div className="relative">
                {isEditing && <EditorToolbar editorRef={editorRef} />}
                 {selectionPosition && isPremiumFeatureEnabled && (
                    <div
                        className="comment-add-button bg-zinc-800 text-white rounded-lg shadow-xl flex items-center"
                        style={{ top: selectionPosition.top, left: selectionPosition.left, transform: 'translateX(-50%)' }}
                    >
                        <button className="p-2 hover:bg-zinc-700 rounded-l-lg" title="Add Comment" aria-label="Add comment" onClick={handleAddCommentClick}><MessageSquarePlusIcon className="w-5 h-5"/></button>
                        <div className="w-px h-5 bg-zinc-600"></div>
                        <button className="p-2 hover:bg-zinc-700 rounded-r-lg" title="Save as Clause" aria-label="Save as clause" onClick={handleSaveClauseClick}><LibraryIcon className="w-5 h-5"/></button>
                    </div>
                )}
                <DocumentRenderer 
                    key={document.id + committedContent} 
                    content={committedContent} 
                    isEditing={isEditing && !isLockedForSigning && !isWorkflowActive} 
                    editorRef={editorRef}
                    onClick={handleEditorClick}
                />
            </div>
        </div>

      </div>
       {isPremiumFeatureEnabled && (
            <DocumentUtilityPanel
                document={document}
                isEditing={isEditing && !isLockedForSigning}
                onInsertClause={handleInsertClause}
                onRevert={onRevert}
                user={user}
                allUsers={allUsers}
                setView={setView}
                documentContent={editorRef.current?.innerText || ''}
                activeTool={activeTool}
                setActiveTool={setActiveTool}
                onAddReply={onAddReply}
                onResolveThread={onResolveThread}
                onDeleteComment={onDeleteComment}
                activityLogs={document.activityLogs || []}
                isOpen={isUtilityPanelOpen}
                onClose={() => setIsUtilityPanelOpen(false)}
                onUpdateObligationStatus={onUpdateObligationStatus}
            />
        )}
    </div>
    <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        document={document}
        currentUser={user}
        allUsers={allUsers}
        onUpdateCollaborators={handleCollaboratorsUpdate}
    />
    <SaveTemplateModal
        isOpen={isSaveTemplateModalOpen}
        onClose={() => setIsSaveTemplateModalOpen(false)}
        onSave={handleSaveTemplate}
    />
     <RequestSignatureModal
        isOpen={isSignatureModalOpen}
        onClose={() => setIsSignatureModalOpen(false)}
        document={document}
        allUsers={allUsers}
        onRequest={onRequestSignatures}
      />
      <RequestApprovalModal
        isOpen={isApprovalModalOpen}
        onClose={() => setIsApprovalModalOpen(false)}
        document={document}
        allUsers={allUsers}
        onRequest={onRequestApproval}
      />
      <ClauseEditorModal
        isOpen={isClauseEditorOpen}
        onClose={() => {setIsClauseEditorOpen(false); setClauseContentToSave(null);}}
        onSave={(data) => {
            onCreateClause(data);
            setIsClauseEditorOpen(false);
            setClauseContentToSave(null);
        }}
        initialContent={clauseContentToSave}
      />
      <InsertClauseModal
        isOpen={isInsertClauseModalOpen}
        onClose={() => setIsInsertClauseModalOpen(false)}
        onInsert={handleInsertClause}
        user={user}
      />
    </>
  );
});

export default DocumentDetailPage;
